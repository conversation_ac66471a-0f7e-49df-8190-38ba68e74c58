using ClosedXML.Excel;
using Microsoft.Extensions.Options;
using System;
using System.IO;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Constants;
using System.Collections.Generic;
using WHO.MALARIA.DocumentManager.Models;
using Microsoft.AspNetCore.Http;
using WHO.MALARIA.Domain.Enum;
using System.Linq;
using WHO.MALARIA.DocumentManager.Exceptions;
using WHO.MALARIA.Domain.SeedingMetadata;
using System.Data;
using WHO.MALARIA.Features.Helpers;
using WHODoaminConstants = WHO.MALARIA.Domain.Constants.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.DocumentManager
{
    /// <summary>
    ///  This class contains methods related to modify excel and perform operation on the each sheet  
    /// </summary>
    public class DQADocumentManager : IDQADocumentManager
    {
        private readonly DQAExcelSetting _dqaExcelSettings;
        private readonly ITranslationService _translationService;

        private readonly Dictionary<string, int> ConsistencyOverTimeIndicators = UtilityHelper.GetEnumerableData<ConsistencyOverTimeKeyIndicator>();

        private string ColumnName = "Percentage";

        public DQADocumentManager(IOptions<DQAExcelSetting> dqaExcelSettings, ITranslationService translationService)
        {
            _dqaExcelSettings = dqaExcelSettings.Value;
            _translationService = translationService;
        }

        #region  Public Method

        /// <summary>
        /// Modify existing dqa excel file template
        /// </summary>
        /// <param name="createTemplateInputModel">Object of CreateTemplateInputModel</param>
        /// <param name="language">language code EN/FR</param>
        /// <returns>byte[]</returns>
        public byte[] ProcessExcel(CreateTemplateInputModel createTemplateInputModel, string language)
        {
            using (MemoryStream memoryStream = new MemoryStream())
            {
                // string directoryPath = Environment.CurrentDirectory;
                // string excelFolderPath = $@"{directoryPath}\Templates\DQA\DQA_SERVICE_{language}.xlsx";

                string directoryPath = Environment.CurrentDirectory;
                string excelFolderPath = Path.Combine(directoryPath, "Templates", "DQA", $"DQA_SERVICE_{language}.xlsx");

                // Check if template file exists
                if (!File.Exists(excelFolderPath))
                {
                    throw new FileNotFoundException($"DQA Service Level template file not found at path: {excelFolderPath}");
                }

                Console.WriteLine($"Processing DQA Service Level template from: {excelFolderPath}");

                using (XLWorkbook workbook = new XLWorkbook(excelFolderPath))
                {

                    #region Instruction

                    IXLWorksheet workbookConfigurationWorksheet = workbook.Worksheet(1);
                    workbookConfigurationWorksheet.SetTabColor(XLColor.Red).Style.Protection.SetLocked(false);
                    workbookConfigurationWorksheet.Protect();

                    IXLWorksheet instructionWorksheet = workbook.Worksheet(2);
                    instructionWorksheet.SetTabColor(XLColor.Red).Style.Protection.SetLocked(false);
                    instructionWorksheet.Protect();

                    #endregion

                    #region Summary Result

                    SummaryWorksheet summaryWorkSheetSetting = _dqaExcelSettings.ServiceLevel.SummaryWorksheet;
                    IXLWorksheet summaryResultWorksheet = workbook.Worksheet(summaryWorkSheetSetting.SheetNumber);

                    summaryResultWorksheet.SetTabColor(XLColor.FromArgb(204, 153, 255)); //Violet
                    summaryResultWorksheet.Protect();

                    summaryResultWorksheet.Cell(summaryWorkSheetSetting.DateCell).SetValue<int>(createTemplateInputModel.Year).Style.Protection.SetHidden().Protection.SetLocked(true);

                    #endregion

                    #region HMIS Data Sheet

                    string[] months = language == Domain.Constants.Constants.Common.DefaultLanguage ? Domain.Constants.Constants.Common.Months : Domain.Constants.Constants.Common.Months_fr;

                    ModifyHMISWorkSheet(workbook, months, createTemplateInputModel);

                    #endregion

                    #region SourceData

                    ModifySourceDataWorkSheet(workbook, months, createTemplateInputModel);

                    #endregion

                    #region 1.2.11 Completeness of Core Result

                    ModifyCompletenessCoreWorkSheet(workbook, createTemplateInputModel);

                    #endregion

                    #region 1.2.2 Concordance of Core Result

                    ModifyConcordanceWorkSheet(workbook, createTemplateInputModel);

                    #endregion

                    #region Reason For Observed Result

                    ModifyObservedResultWorkSheet(workbook, createTemplateInputModel);

                    #endregion

                    #region Correction Result

                    ModifyCorrectionWorkSheet(workbook, createTemplateInputModel);

                    #endregion

                    #region Hidden Sheet

                    // Creating additional hidden sheet which hold the ServiceLevelId so that it can be validated against the uploaded file
                    IXLWorksheet hiddenResultWorksheet = workbook.Worksheets.Add(workbook.Worksheets.Count + 1);
                    hiddenResultWorksheet.Cell("A1").SetValue<Guid>(createTemplateInputModel.ServiceLevelId).Style.Protection.SetHidden().Protection.SetLocked(true);
                    hiddenResultWorksheet.Cell("A2").SetValue<Int16>(createTemplateInputModel.Version).Style.Protection.SetHidden().Protection.SetLocked(true);
                    hiddenResultWorksheet.Hide().Protect();

                    #endregion

                    workbook.SaveAs(memoryStream);
                }

                return memoryStream.ToArray();
            }
        }

        /// <summary>
        /// Read DQA service level excel summary sheet
        /// </summary>
        /// <param name="file">template file</param>     
        /// <returns>Template summary data</returns>
        public ServiceLevelSummaryViewModel ReadSLSummarySheet(IFormFile file)
        {
            ServiceLevelSummaryViewModel serviceLevelSummaryViewModel = new ServiceLevelSummaryViewModel();

            using (var memorystream = new MemoryStream())
            {
                file.CopyTo(memorystream);
                {
                    using (XLWorkbook workbook = new XLWorkbook(memorystream))
                    {
                        IXLWorksheet summaryWorksheet = workbook.Worksheet(_dqaExcelSettings.ServiceLevel.SummaryWorksheet.SheetNumber); //Summary Sheet

                        //check summary sheet contain value empty or error
                        if (IsServiceLevelSummarySheetValid(summaryWorksheet, file.FileName))
                        {
                            object total = summaryWorksheet.Cell("C5").CachedValue;
                            if (!(total is null || total is ""))
                            {
                                serviceLevelSummaryViewModel.Total = Convert.ToDouble(total);
                            }

                            object sex = summaryWorksheet.Cell("C6").CachedValue;
                            if (!(sex is null || sex is ""))
                            {
                                serviceLevelSummaryViewModel.Sex = Convert.ToDouble(sex);
                            }

                            object age = summaryWorksheet.Cell("C7").CachedValue;
                            if (!(age is null || age is ""))
                            {
                                serviceLevelSummaryViewModel.Age = Convert.ToDouble(age);
                            }

                            object diagnosis = summaryWorksheet.Cell("C8").CachedValue;
                            if (!(diagnosis is null || diagnosis is ""))
                            {
                                serviceLevelSummaryViewModel.Diagnosis = Convert.ToDouble(diagnosis);
                            }

                            serviceLevelSummaryViewModel.VariableData = new List<ServiceLevelVariableDataViewModel>();

                            // Reading excel summary sheet from row 10 to row 21 for concordance of core variables
                            for (int rowCellIndex = 10; rowCellIndex <= 21; rowCellIndex++)
                            {
                                string variableName = summaryWorksheet.Cell($"B{rowCellIndex}").Value?.ToString();

                                ServiceLevelVariableDataViewModel serviceLevelVariableData = new ServiceLevelVariableDataViewModel();

                                serviceLevelVariableData.Name = variableName;

                                object monthConcordance = summaryWorksheet.Cell($"C{rowCellIndex}").CachedValue;
                                if (!(monthConcordance is null || monthConcordance is "" || monthConcordance is "#DIV/0!"))
                                {
                                    serviceLevelVariableData.MonthConcordance = Convert.ToDouble(monthConcordance);
                                }

                                object errorInDataSource = summaryWorksheet.Cell($"D{rowCellIndex}").CachedValue;
                                if (!(errorInDataSource is null || errorInDataSource is ""))
                                {
                                    serviceLevelVariableData.ErrorInDataSource = Convert.ToDouble(errorInDataSource);
                                }
                                serviceLevelSummaryViewModel.VariableData.Add(serviceLevelVariableData);
                            }
                            IXLWorksheet hiddenWorksheet = workbook.Worksheet(workbook.Worksheets.Count); //Hidden Sheet
                            serviceLevelSummaryViewModel.ServiceLevelId = Guid.Parse(hiddenWorksheet.Cell("A1").Value?.ToString());
                            serviceLevelSummaryViewModel.Version = Int16.Parse(hiddenWorksheet.Cell("A2").Value?.ToString());
                        }
                    }
                }
            }

            return serviceLevelSummaryViewModel;
        }

        /// <summary>
        /// Generate excel template workbook for desk level
        /// </summary>
        /// <param name="templateHeaderRow">List of headers for worksheets</param>
        /// <param name="sheetDetails">List of instance of DeskLevelExcelTemplateSheetDetails- Contains sheet details that are being added into the Workbook</param>
        /// <returns>Excel workbook response in byte array</returns>
        public byte[] GenerateDeskLevelTemplate(List<DeskLevelExcelTemplateSheetDetails> sheetDetails, string language)
        {
            using (MemoryStream memoryStream = new MemoryStream())
            {
                var workbook = new XLWorkbook();
                string sourceFilePath = "";
                string sourceSheetName = "";
                string destinationSheetName = "";                

                if (language == "en")
                {
                    sourceFilePath = Constants.DqaIntroductionTab_EN;
                    sourceSheetName = Constants.IntroductionTabSourceSheetName_EN;
                    destinationSheetName = Constants.IntroductionTabSourceSheetName_EN;
                }
                else
                {
                    sourceFilePath = Constants.DqaIntroductionTab_FR;
                    sourceSheetName = Constants.IntroductionTabSourceSheetName_FR;
                    destinationSheetName = Constants.IntroductionTabSourceSheetName_FR;
                }
                
                if (!File.Exists(sourceFilePath))
                {
                    throw new FileNotFoundException($"DQA Desk Level introduction template file not found at path: {sourceFilePath}");
                }

                Console.WriteLine($"Processing DQA Desk Level template from: {sourceFilePath}");

                // Load the source workbook
                using (var sourceWorkbook = new XLWorkbook(sourceFilePath))
                {
                    var sourceWorksheet = sourceWorkbook.Worksheet(sourceSheetName);

                    // Create a new workbook 
                    using (var destinationWorkbook = new XLWorkbook())
                    {
                        // Add a new worksheet in the destination workbook
                        var destinationWorksheet = workbook.Worksheets.Add(destinationSheetName);

                        // Copy all cells from the source worksheet to the destination worksheet
                        foreach (var cell in sourceWorksheet.CellsUsed())
                        {
                            destinationWorksheet.Cell(cell.Address).Value = cell.Value;
                            destinationWorksheet.Cell(cell.Address).Style = cell.Style;
                        }
                        foreach (var row in sourceWorksheet.RowsUsed())
                        {
                            destinationWorksheet.Row(row.RowNumber()).Height = row.Height;
                        }

                        // Copy column widths
                        foreach (var column in sourceWorksheet.ColumnsUsed())
                        {
                            destinationWorksheet.Column(column.ColumnNumber()).Width = column.Width;
                        }

                        // Copy merged cells
                        foreach (var mergedRange in sourceWorksheet.MergedRanges)
                        {
                            var firstAddress = mergedRange.RangeAddress.FirstAddress;
                            var lastAddress = mergedRange.RangeAddress.LastAddress;
                            destinationWorksheet.Range(firstAddress.RowNumber, firstAddress.ColumnNumber, lastAddress.RowNumber, lastAddress.ColumnNumber).Merge();
                        }
                    }
                }

                sheetDetails.ForEach((DeskLevelExcelTemplateSheetDetails sheetDetail) =>
                {
                    IXLWorksheet worksheet = workbook.Worksheets.Add(sheetDetail.Name);

                    worksheet.Style.Fill.BackgroundColor = XLColor.NoColor;

                    // Style for worksheet title
                    IXLStyle titleStyle = workbook.Style;
                    titleStyle.Font.Bold = true;
                    titleStyle.Font.FontName = "Calibri";
                    titleStyle.Font.FontSize = 18;
                    titleStyle.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                    titleStyle.Fill.BackgroundColor = XLColor.NoColor;

                    // Set title value and format worksheet title
                    worksheet.Range(1, 1, 2, 10).Merge().Value = sheetDetail.Title;
                    worksheet.Range(1, 1, 2, 10).Merge().Style = titleStyle;

                    DataTable table = ToDataTable(sheetDetail.Variables);

                    //Set values to prepare the titles
                    sheetDetail.PriorityVariableCount = sheetDetail.PriorityVariableCount == 0 ? 1 : sheetDetail.PriorityVariableCount;
                    sheetDetail.OptionalVariableCount = sheetDetail.OptionalVariableCount == 0 ? 1 : sheetDetail.OptionalVariableCount;

                    worksheet.Cell(4, 1).InsertTable(table);

                    // Style for Disaggregation variables
                    IXLStyle disaggregationTitleStyle = workbook.Style;
                    disaggregationTitleStyle.Font.Bold = true;
                    disaggregationTitleStyle.Font.FontName = "Calibri";
                    disaggregationTitleStyle.Font.FontSize = 10;
                    disaggregationTitleStyle.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                    disaggregationTitleStyle.Fill.BackgroundColor = XLColor.LightBlue;

                    // Format all titles for disaggregation variables
                    worksheet.Range(4, 1, 4, sheetDetail.DisaggregationVariableCount).Style = disaggregationTitleStyle;

                    // Style for priority variables
                    IXLStyle priorityTitleStyle = workbook.Style;
                    priorityTitleStyle.Font.Bold = true;
                    priorityTitleStyle.Font.FontName = "Calibri";
                    priorityTitleStyle.Font.FontSize = 10;
                    priorityTitleStyle.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                    priorityTitleStyle.Fill.BackgroundColor = XLColor.LightGray;

                    // Format all titles for priority variables
                    worksheet.Range(4,
                                    sheetDetail.DisaggregationVariableCount + 1,
                                    4,
                                    sheetDetail.DisaggregationVariableCount + sheetDetail.PriorityVariableCount).Style = priorityTitleStyle;

                    // Format priority variables title 
                    worksheet.Range(3, sheetDetail.DisaggregationVariableCount + 1,
                                    3,
                                    sheetDetail.DisaggregationVariableCount + sheetDetail.PriorityVariableCount).Merge().Value =
                                    language == WHODoaminConstants.Common.DefaultLanguage ?
                                    "Variables - Priority" : "Variables - prioritaires";

                    worksheet.Range(3,
                                      sheetDetail.DisaggregationVariableCount + 1,
                                      3,
                                      sheetDetail.DisaggregationVariableCount + sheetDetail.PriorityVariableCount).Merge().Style = priorityTitleStyle;

                    // Style for optional variables
                    IXLStyle optionalTitleStyle = workbook.Style;
                    optionalTitleStyle.Font.Bold = true;
                    optionalTitleStyle.Font.FontName = "Calibri";
                    optionalTitleStyle.Font.FontSize = 10;
                    optionalTitleStyle.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                    optionalTitleStyle.Fill.BackgroundColor = XLColor.LightPastelPurple;

                    // Format all titles for optional variables
                    worksheet.Range(4,
                                    sheetDetail.DisaggregationVariableCount + sheetDetail.PriorityVariableCount + 1,
                                    4,
                                    sheetDetail.DisaggregationVariableCount + sheetDetail.PriorityVariableCount + sheetDetail.OptionalVariableCount).Style = optionalTitleStyle;

                    // Format priority optional title 
                    worksheet.Range(3,
                                    sheetDetail.DisaggregationVariableCount + sheetDetail.PriorityVariableCount + 1,
                                    3,
                                    sheetDetail.DisaggregationVariableCount + sheetDetail.PriorityVariableCount + sheetDetail.OptionalVariableCount).Merge().Value =
                                    language == WHODoaminConstants.Common.DefaultLanguage ?
                                    "Variables - Optional" : "Variables - facultatives";

                    worksheet.Range(3,
                                      sheetDetail.DisaggregationVariableCount + sheetDetail.PriorityVariableCount + 1,
                                      3,
                                      sheetDetail.DisaggregationVariableCount + sheetDetail.PriorityVariableCount + sheetDetail.OptionalVariableCount).Merge().Style = optionalTitleStyle;

                    // Set fixed column widths instead of AdjustToContents() to avoid System.Drawing.Common dependency on non-Windows platforms
                    worksheet.Columns().Width = 15; // Set a reasonable default width for all columns
                });

                workbook.SaveAs(memoryStream);

                return memoryStream.ToArray();
            }
        }

        /// <summary>
        /// Generate excel report workbook for Desk level DQA
        /// </summary>
        /// <param name="sheetDetails">List of instance of DeskLevelExcelTemplateSheetDetails- Contains sheet details that are being added into the Workbook</param>
        /// <returns>Excel workbook response in byte array</returns>
        public byte[] GenerateDeskLevelReport(List<DeskLevelExcelTemplateSheetDetails> sheetDetails, List<DQAVariableDto> dQAVariableDtos, string language)
        {
            using (MemoryStream memoryStream = new MemoryStream())
            {
                string templateFilePath = Path.Combine(Environment.CurrentDirectory, DQAConstants.DQATemplateFilePath, $"{_dqaExcelSettings.DeskLevel.ReportFileName}_{language}.xlsx");

                // Check if template file exists
                if (!File.Exists(templateFilePath))
                {
                    throw new FileNotFoundException($"DQA Desk Level report template file not found at path: {templateFilePath}");
                }

                Console.WriteLine($"Processing DQA Desk Level report from: {templateFilePath}");

                var workbook = new XLWorkbook(templateFilePath);

                sheetDetails.ForEach((DeskLevelExcelTemplateSheetDetails sheetDetail) =>
                {
                    if (!string.IsNullOrEmpty(sheetDetail.Name) && !string.IsNullOrEmpty(sheetDetail.Title) && sheetDetail.ReportDetails != null && sheetDetail.ReportDetails.Count > 0)
                    {
                        IXLWorksheet worksheet = workbook.Worksheets.FirstOrDefault(x => x.Name == sheetDetail.Name);

                        if (worksheet != null)
                        {
                            worksheet.Style.Alignment.SetVertical(XLAlignmentVerticalValues.Top);
                            worksheet.Style.Fill.BackgroundColor = XLColor.NoColor;
                            worksheet.Style.Font.Bold = false;
                            worksheet.Style.Font.FontName = "Calibri";
                            worksheet.Style.Font.FontSize = 10;
                            worksheet.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.General;

                            // Style for worksheet title
                            IXLStyle titleStyle = workbook.Style;
                            titleStyle.Font.Bold = true;
                            titleStyle.Font.FontName = "Calibri";
                            titleStyle.Font.FontSize = 18;
                            titleStyle.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                            titleStyle.Fill.BackgroundColor = XLColor.FromArgb(189, 215, 238);

                            // Set title value and format of worksheet title
                            worksheet.Range(1, 1, 2, 12).Merge().Value = sheetDetail.Title;
                            worksheet.Range(1, 1, 2, 12).Merge().Style = titleStyle;

                            int rowCount = 0;
                            int columnCount = 0;

                            foreach (TableReportDetails response in sheetDetail.ReportDetails)
                            {
                                DataTable table = response.ReportTable;

                                IXLStyle reportTitleStyle = workbook.Style;
                                reportTitleStyle.Font.Bold = true;
                                reportTitleStyle.Font.FontName = "Calibri";
                                reportTitleStyle.Font.FontSize = 10;
                                reportTitleStyle.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                                reportTitleStyle.Fill.BackgroundColor = XLColor.FromArgb(146, 208, 80);

                                //Set title of National report
                                worksheet.Range(_dqaExcelSettings.DeskLevel.FirstReportTitleFirstCellRow + rowCount,
                                                _dqaExcelSettings.DeskLevel.FirstReportTitleFirstCellColumn,
                                                _dqaExcelSettings.DeskLevel.FirstReportTitleLastCellRow + rowCount,
                                                _dqaExcelSettings.DeskLevel.FirstReportTitleLastCellColumn).Merge().Value = response.Title;

                                worksheet.Range(_dqaExcelSettings.DeskLevel.FirstReportTitleFirstCellRow + rowCount,
                                                _dqaExcelSettings.DeskLevel.FirstReportTitleFirstCellColumn,
                                                _dqaExcelSettings.DeskLevel.FirstReportTitleLastCellRow + rowCount,
                                                _dqaExcelSettings.DeskLevel.FirstReportTitleLastCellColumn).Merge().Style = reportTitleStyle;

                                worksheet.Range(_dqaExcelSettings.DeskLevel.FirstReportTitleFirstCellRow + rowCount,
                                                _dqaExcelSettings.DeskLevel.FirstReportTitleFirstCellColumn,
                                                _dqaExcelSettings.DeskLevel.FirstReportTitleLastCellRow + rowCount,
                                                _dqaExcelSettings.DeskLevel.FirstReportTitleLastCellColumn).Merge().Style.Border.OutsideBorder = XLBorderStyleValues.Thin;

                                table = RemoveExtraColumns(response.ReportTable);

                                worksheet.Cell(_dqaExcelSettings.DeskLevel.FirstReportRow + rowCount, _dqaExcelSettings.DeskLevel.FirstReportColumn).InsertTable(table)
                                .Style.Border.SetInsideBorder(XLBorderStyleValues.Thin).Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                                //Set fixed row and column width for long content (avoiding System.Drawing.Common dependency)
                                worksheet.Columns().Width = 20; // Set reasonable column width
                                worksheet.Rows().Height = 25; // Set reasonable row height
                                worksheet.Rows().Style.Alignment.SetWrapText(true);

                                if (response.ReportTable.TableName == "National Summary Report")
                                    foreach ((string, XLColor) rule in GetColorsRule(response.ReportTable, language))
                                    {
                                        worksheet.Range("C1:C600").AddConditionalFormat().WhenContains(rule.Item1).Fill.SetBackgroundColor(rule.Item2);
                                    }

                                if (response.ReportTable.TableName == "National Summary Report")
                                {
                                    int lastRowIndex = _dqaExcelSettings.DeskLevel.FirstReportRow + table.Rows.Count + 2; // +2 to account for 1-based index and header row

                                    int totalColumns = response.ReportTable.Columns.Count;
                                    var rangeToMerge = worksheet.Range(lastRowIndex, 2, lastRowIndex, totalColumns + 6);

                                    // Merge the cells in the range
                                    rangeToMerge.Merge();

                                    // Set the value of the merged cell
                                    if (language == "en")
                                    {
                                        rangeToMerge.FirstCell().Value = "*Color key for data with percentage: red cells indicate percentage is less than 80% and greater than 100%; yellow cells indicate percentage is between 80%-95%; and green cells indicate percentage is greater than 95%";
                                    }
                                    else
                                    {
                                        rangeToMerge.FirstCell().Value = "* Clé de couleur pour les données avec pourcentage : les cellules rouges indiquent que le pourcentage est inférieur à 80 % et supérieur à 100 % ; Les cellules jaunes indiquent que le pourcentage est compris entre 80 % et 95 % et les cellules vertes indiquent que le pourcentage est supérieur à 95 %";
                                    }
                                    rangeToMerge.Style.Alignment.WrapText = true;
                                    worksheet.Row(lastRowIndex).Height = 30; // Set fixed row height instead of AdjustToContents()
                                    rangeToMerge.FirstCell().Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Left;
                                    rangeToMerge.FirstCell().Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;
                                }
                                worksheet.RangeUsed().AddConditionalFormat().WhenEquals("Yes").Fill.SetBackgroundColor(XLColor.FromArgb(198, 239, 206));
                                worksheet.RangeUsed().AddConditionalFormat().WhenEquals("No").Fill.SetBackgroundColor(XLColor.FromArgb(255, 199, 206));
                                worksheet.RangeUsed().AddConditionalFormat().WhenEquals(MetNotMetStatus.Met.GetTranslationKey()).Fill.SetBackgroundColor(XLColor.FromArgb(198, 239, 206));
                                worksheet.RangeUsed().AddConditionalFormat().WhenEquals(MetNotMetStatus.NotMet.GetTranslationKey()).Fill.SetBackgroundColor(XLColor.FromArgb(255, 199, 206));

                                switch (sheetDetail.NameTranslationKey)
                                {
                                    case DQAConstants.CompletenessOfReporting:

                                        IXLWorksheet reportingCompletenessGraphWorksheet = workbook.Worksheets
                                        .FirstOrDefault(x => x.Name == _translationService.GetTranslation(DQAConstants.CompletenessOfReportingGraph));

                                        CompletenessOfReportingChartSheetSetting(reportingCompletenessGraphWorksheet, response.TitleTranslationKey, response.Type, response.GraphReport);

                                        break;

                                    case DQAConstants.TimelinessOfReporting:

                                        IXLWorksheet reportingTimelinessGraphWorksheet = workbook.Worksheets
                                        .FirstOrDefault(x => x.Name == _translationService.GetTranslation(DQAConstants.TimelinessOfReportingGraph));

                                        TimelinessOfReportingChartSheetSetting(reportingTimelinessGraphWorksheet, response.TitleTranslationKey, response.Type, response.GraphReport);

                                        break;

                                    case DQAConstants.VariableCompleteness:

                                        IXLWorksheet variableCompletenessGraphWorksheet = workbook.Worksheets
                                        .FirstOrDefault(x => x.Name == _translationService.GetTranslation(DQAConstants.VariableCompletenessGraph));

                                        VariableCompletenessChartSheetSetting(variableCompletenessGraphWorksheet, response.TitleTranslationKey, response.Type, response.GraphReport);

                                        break;

                                    case DQAConstants.ConsistencyBetweenVariable:

                                        IXLWorksheet consistencyBetweenVariableGraphWorksheet = workbook.Worksheets
                                        .FirstOrDefault(x => x.Name == _translationService.GetTranslation(DQAConstants.ConsistencyBetweenVariableGraph));

                                        ConsistencyBetweenVariableChartSheetSetting(consistencyBetweenVariableGraphWorksheet, response.TitleTranslationKey, response.Type, response.GraphReport);

                                        break;

                                    case DQAConstants.ConsistencyOverTime:

                                        IXLWorksheet consistencyOverTimeGraphWorksheet = workbook.Worksheets
                                        .FirstOrDefault(x => x.Name.Contains(_translationService.GetTranslation(DQAConstants.ConsistencyOverTimeGraph)));

                                        ConsistencyOverTimeChartSheetSetting(consistencyOverTimeGraphWorksheet, response.TitleTranslationKey, response.Type, response.GraphReport);

                                        break;

                                    case DQAConstants.Concordance:

                                        IXLWorksheet concordanceGraphWorksheet = workbook.Worksheets
                                        .FirstOrDefault(x => x.Name == _translationService.GetTranslation(DQAConstants.ConcordanceGraph));

                                        ConcordanceChartSheetSetting(concordanceGraphWorksheet, response.TitleTranslationKey, response.Type, response.GraphReport);

                                        break;
                                }

                                rowCount += table.Rows.Count + 4;
                                columnCount += table.Columns.Count + 4;
                            }
                        }
                    }
                });
                workbook.Worksheet(1).SetTabActive();
                foreach (var worksheet in workbook.Worksheets)
                {
                    foreach (var column in worksheet.ColumnsUsed())
                    {
                        // Set all cells in the column to left justification - bug 97237
                        column.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Left;
                    }
                }
                workbook.SaveAs(memoryStream);

                return memoryStream.ToArray();
            }
        }

        /// <summary>
        /// Generate excel report workbook for elimination DQA
        /// </summary>
        /// <param name="sheetDetails">List of instance of DeskLevelExcelTemplateSheetDetails- Contains sheet details that are being added into the Workbook</param>
        /// <returns>Excel workbook response in byte array</returns>
        public byte[] GenerateEliminationReport(List<DeskLevelExcelTemplateSheetDetails> sheetDetails)
        {
            using (MemoryStream memoryStream = new MemoryStream())
            {
                var workbook = new XLWorkbook();

                sheetDetails.ForEach((DeskLevelExcelTemplateSheetDetails sheetDetail) =>
                {

                    if (!string.IsNullOrEmpty(sheetDetail.Name) && !string.IsNullOrEmpty(sheetDetail.Title) && sheetDetail.ReportDetails != null && sheetDetail.ReportDetails.Count > 0)
                    {
                        IXLWorksheet worksheet = workbook.Worksheets.Add(sheetDetail.Name);

                        worksheet.Style.Alignment.SetVertical(XLAlignmentVerticalValues.Top);

                        worksheet.Style.Fill.BackgroundColor = XLColor.NoColor;
                        worksheet.Style.Font.Bold = false;
                        worksheet.Style.Font.FontName = "Calibri";
                        worksheet.Style.Font.FontSize = 10;
                        worksheet.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.General;

                        // Style for worksheet title
                        IXLStyle titleStyle = workbook.Style;
                        titleStyle.Font.Bold = true;
                        titleStyle.Font.FontName = "Calibri";
                        titleStyle.Font.FontSize = 18;
                        titleStyle.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                        titleStyle.Fill.BackgroundColor = XLColor.FromArgb(189, 215, 238);

                        // Set title value and format of worksheet title
                        worksheet.Range(1, 1, 2, 12).Merge().Value = sheetDetail.Title;
                        worksheet.Range(1, 1, 2, 12).Merge().Style = titleStyle;

                        int rowCount = 0;
                        int columnCount = 0;

                        foreach (TableReportDetails response in sheetDetail.ReportDetails)
                        {
                            DataTable table = response.ReportTable;

                            IXLStyle reportTitleStyle = workbook.Style;
                            reportTitleStyle.Font.Bold = true;
                            reportTitleStyle.Font.FontName = "Calibri";
                            reportTitleStyle.Font.FontSize = 10;
                            reportTitleStyle.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                            reportTitleStyle.Fill.BackgroundColor = XLColor.FromArgb(146, 208, 80);

                            //Set title of National report
                            worksheet.Range(_dqaExcelSettings.DeskLevel.FirstReportTitleFirstCellRow + rowCount,
                                            _dqaExcelSettings.DeskLevel.FirstReportTitleFirstCellColumn,
                                            _dqaExcelSettings.DeskLevel.FirstReportTitleLastCellRow + rowCount,
                                            _dqaExcelSettings.DeskLevel.FirstReportTitleLastCellColumn).Merge().Value = response.Title;

                            worksheet.Range(_dqaExcelSettings.DeskLevel.FirstReportTitleFirstCellRow + rowCount,
                                            _dqaExcelSettings.DeskLevel.FirstReportTitleFirstCellColumn,
                                            _dqaExcelSettings.DeskLevel.FirstReportTitleLastCellRow + rowCount,
                                            _dqaExcelSettings.DeskLevel.FirstReportTitleLastCellColumn).Merge().Style = reportTitleStyle;

                            worksheet.Range(_dqaExcelSettings.DeskLevel.FirstReportTitleFirstCellRow + rowCount,
                                            _dqaExcelSettings.DeskLevel.FirstReportTitleFirstCellColumn,
                                            _dqaExcelSettings.DeskLevel.FirstReportTitleLastCellRow + rowCount,
                                            _dqaExcelSettings.DeskLevel.FirstReportTitleLastCellColumn).Merge().Style.Border.OutsideBorder = XLBorderStyleValues.Thin;

                            worksheet.Cell(_dqaExcelSettings.DeskLevel.FirstReportRow + rowCount, _dqaExcelSettings.DeskLevel.FirstReportColumn).InsertTable(table)
                            .Style.Border.SetInsideBorder(XLBorderStyleValues.Thin).Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                            //Set fixed row and column width for long content (avoiding System.Drawing.Common dependency)
                            worksheet.Columns().Width = 25; // Set reasonable column width
                            worksheet.Rows().Height = 25; // Set reasonable row height
                            worksheet.Rows().Style.Alignment.SetWrapText(true);

                            //Highlight cells based on range
                            worksheet.RangeUsed().AddConditionalFormat().WhenBetween("1", "80").Fill.SetBackgroundColor(XLColor.FromArgb(255, 199, 206));
                            worksheet.RangeUsed().AddConditionalFormat().WhenBetween("80", "95").Fill.SetBackgroundColor(XLColor.FromArgb(255, 235, 156));
                            worksheet.RangeUsed().AddConditionalFormat().WhenBetween("95", "999").Fill.SetBackgroundColor(XLColor.FromArgb(198, 239, 206));

                            //TODO: Need to cross check below 3 lines as it is not highlighting text values in excel (national level result Summary data)
                            worksheet.RangeUsed().AddConditionalFormat().WhenBetween("1", "80").Fill.SetBackgroundColor(XLColor.FromArgb(255, 199, 206)).SetIncludeQuotePrefix(true);
                            worksheet.RangeUsed().AddConditionalFormat().WhenBetween("80", "95").Fill.SetBackgroundColor(XLColor.FromArgb(255, 235, 156)).SetIncludeQuotePrefix(true);
                            worksheet.RangeUsed().AddConditionalFormat().WhenBetween("95", "999").Fill.SetBackgroundColor(XLColor.FromArgb(198, 239, 206)).SetIncludeQuotePrefix(true);

                            worksheet.RangeUsed().AddConditionalFormat().WhenEquals("Yes").Fill.SetBackgroundColor(XLColor.FromArgb(198, 239, 206));
                            worksheet.RangeUsed().AddConditionalFormat().WhenEquals("No").Fill.SetBackgroundColor(XLColor.FromArgb(255, 199, 206));

                            rowCount += table.Rows.Count + 4;
                            columnCount += table.Columns.Count + 4;
                        }
                    }
                });

                workbook.SaveAs(memoryStream);

                return memoryStream.ToArray();
            }
        }

        /// <summary>
        /// Get CompletenessOfReporting Chart Sheet Setting
        /// </summary>
        /// <param name="graphSheet">Instance of IXLWorksheet</param>
        /// <param name="title">Title of sheet</param>
        /// <param name="type">Type of graph - refer Enum DeskLevelGraphTypes</param>
        /// <param name="graphReport">Report data</param>
        public void CompletenessOfReportingChartSheetSetting(IXLWorksheet graphSheet, string title, int type, dynamic graphReport)
        {
            if (graphSheet == null || string.IsNullOrEmpty(title))
            {
                throw new ApplicationException();
            }

            switch (title)
            {
                case DQAConstants.NationalReport:

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport,
                                _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.CompletenessOfReportingGraph.NationalLevelType1Graph.InitialReportRow,
                                _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.CompletenessOfReportingGraph.NationalLevelType1Graph.InitialReportColumn);

                            break;

                        case (int)DeskLevelGraphTypes.Type1:


                            InsertTableInExcelSheet(graphSheet, graphReport,
                                _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.CompletenessOfReportingGraph.NationalLevelType2Graph.InitialReportRow,
                                _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.CompletenessOfReportingGraph.NationalLevelType2Graph.InitialReportColumn);

                            break;
                    }

                    break;

                case DQAConstants.ProvinceReport:

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.CompletenessOfReportingGraph.ProvinceLevelType1Graph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.CompletenessOfReportingGraph.ProvinceLevelType1Graph.InitialReportColumn);

                            break;
                    }

                    break;

                case DQAConstants.DistrictReport:

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.CompletenessOfReportingGraph.DistrictType1Graph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.CompletenessOfReportingGraph.DistrictType1Graph.InitialReportColumn);

                            break;

                    }

                    break;
            }
        }

        /// <summary>
        /// Get TimelinessOfReporting Chart Sheet Setting
        /// </summary>
        /// <param name="graphSheet">Instance of IXLWorksheet</param>
        /// <param name="title">Title of sheet</param>
        /// <param name="type">Type of graph - refer Enum DeskLevelGraphTypes</param>
        /// <param name="graphReport">Report data</param>
        public void TimelinessOfReportingChartSheetSetting(IXLWorksheet graphSheet, string title, int type, dynamic graphReport)
        {
            if (graphSheet == null || string.IsNullOrEmpty(title))
            {
                throw new ApplicationException();
            }

            switch (title)
            {
                case DQAConstants.NationalReport:

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.TimelinessOfReportingGraph.NationalLevelType1Graph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.TimelinessOfReportingGraph.NationalLevelType1Graph.InitialReportColumn);


                            break;

                        case (int)DeskLevelGraphTypes.Type1:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.TimelinessOfReportingGraph.NationalLevelType2Graph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.TimelinessOfReportingGraph.NationalLevelType2Graph.InitialReportColumn);


                            break;
                    }

                    break;

                case DQAConstants.ProvinceReport:

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.TimelinessOfReportingGraph.ProvinceLevelType1Graph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.TimelinessOfReportingGraph.ProvinceLevelType1Graph.InitialReportColumn);

                            break;
                    }

                    break;

                case DQAConstants.DistrictReport:

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.TimelinessOfReportingGraph.DistrictType1Graph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.TimelinessOfReportingGraph.DistrictType1Graph.InitialReportColumn);


                            break;

                    }

                    break;
            }
        }

        /// <summary>
        /// Get VariableCompleteness Chart Sheet Setting
        /// </summary>
        /// <param name="graphSheet">Instance of IXLWorksheet</param>
        /// <param name="title">Title of sheet</param>
        /// <param name="type">Type of graph - refer Enum DeskLevelGraphTypes</param>
        /// <param name="graphReport">Report data</param>
        public void VariableCompletenessChartSheetSetting(IXLWorksheet graphSheet, string title, int type, dynamic graphReport)
        {
            if (graphSheet == null || string.IsNullOrEmpty(title))
            {
                throw new ApplicationException();
            }

            switch (title)
            {
                case (DQAConstants.NationalReport + " - " + DQAConstants.CompletenessReportResultHeader):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type3:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.VariableCompletenessGraph.NationalLevelSummaryGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.VariableCompletenessGraph.NationalLevelSummaryGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.ProvinceReport + " - " + DQAConstants.CompletenessReportResultHeader):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type3:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.VariableCompletenessGraph.ProvinceLevelSummaryGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.VariableCompletenessGraph.ProvinceLevelSummaryGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.DistrictReport + " - " + DQAConstants.CompletenessReportResultHeader):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type3:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.VariableCompletenessGraph.DistrictLevelSummaryGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.VariableCompletenessGraph.DistrictLevelSummaryGraph.InitialReportColumn);


                            break;
                    }

                    break;
            }
        }

        /// <summary>
        /// Get ConsistencyBetweenVariable Chart Sheet Setting
        /// </summary>
        /// <param name="graphSheet">Instance of IXLWorksheet</param>
        /// <param name="title">Title of sheet</param>
        /// <param name="type">Type of graph - refer Enum DeskLevelGraphTypes</param>
        /// <param name="graphReport">Report data</param>
        public void ConsistencyBetweenVariableChartSheetSetting(IXLWorksheet graphSheet, string title, int type, dynamic graphReport)
        {
            if (graphSheet == null || string.IsNullOrEmpty(title))
            {
                throw new ApplicationException();
            }

            switch (title)
            {
                case (DQAConstants.NationalReport + " - " + DQAConstants.ConsistencyReportResultHeader):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type3:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyBetweenVariableGraph.NationalLevelSummaryGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyBetweenVariableGraph.NationalLevelSummaryGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.ProvinceReport + " - " + DQAConstants.ConsistencyReportResultHeader):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type3:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyBetweenVariableGraph.ProvinceLevelSummaryGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyBetweenVariableGraph.ProvinceLevelSummaryGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.DistrictReport + " - " + DQAConstants.ConsistencyReportResultHeader):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type3:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyBetweenVariableGraph.DistrictLevelSummaryGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyBetweenVariableGraph.DistrictLevelSummaryGraph.InitialReportColumn);


                            break;
                    }

                    break;
            }
        }

        /// <summary>
        /// Get Concordance Chart Sheet Setting
        /// </summary>
        /// <param name="graphSheet">Instance of IXLWorksheet</param>
        /// <param name="title">Title of sheet</param>
        /// <param name="type">Type of graph - refer Enum DeskLevelGraphTypes</param>
        /// <param name="graphReport">Report data</param>
        public void ConcordanceChartSheetSetting(IXLWorksheet graphSheet, string title, int type, dynamic graphReport)
        {
            if (graphSheet == null || string.IsNullOrEmpty(title))
            {
                throw new ApplicationException();
            }

            switch (title)
            {
                case (DQAConstants.NationalReport + " - " + DQAConstants.ConcordanceReportResultHeader):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type3:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConcordanceGraph.NationalLevelSummaryGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConcordanceGraph.NationalLevelSummaryGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.ProvinceReport + " - " + DQAConstants.ConcordanceReportResultHeader):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type3:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConcordanceGraph.ProvinceLevelSummaryGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConcordanceGraph.ProvinceLevelSummaryGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.DistrictReport + " - " + DQAConstants.ConcordanceReportResultHeader):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type3:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConcordanceGraph.DistrictLevelSummaryGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConcordanceGraph.DistrictLevelSummaryGraph.InitialReportColumn);


                            break;
                    }

                    break;
            }
        }

        /// <summary>
        /// Get ConsistencyOverTime Chart Sheet Setting
        /// </summary>
        /// <param name="graphSheet">Instance of IXLWorksheet</param>
        /// <param name="title">Title of sheet</param>
        /// <param name="type">Type of graph - refer Enum DeskLevelGraphTypes</param>
        /// <param name="graphReport">Report data</param>
        public void ConsistencyOverTimeChartSheetSetting(IXLWorksheet graphSheet, string title, int type, dynamic graphReport)
        {
            if (graphSheet == null || string.IsNullOrEmpty(title))
            {
                throw new ApplicationException();
            }

            switch (title)
            {
                //National Level 
                case (DQAConstants.NationalReport + " - " + DQAConstants.ProportionOfMalariaOutpatients):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.NationalLevelGraph.ProportionOfMalariaOutpatientsGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.NationalLevelGraph.ProportionOfMalariaOutpatientsGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.NationalReport + " - " + DQAConstants.ProportionOfMalariaInpatients):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.NationalLevelGraph.ProportionOfMalariaInpatientsGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.NationalLevelGraph.ProportionOfMalariaInpatientsGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.NationalReport + " - " + DQAConstants.ProportionOfMalariaInpatientDeaths):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.NationalLevelGraph.ProportionOfMalariaInpatientDeathsGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.NationalLevelGraph.ProportionOfMalariaInpatientDeathsGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.NationalReport + " - " + DQAConstants.TestPositivityRate):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.NationalLevelGraph.TestPositivityRateGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.NationalLevelGraph.TestPositivityRateGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.NationalReport + " - " + DQAConstants.SlidePositivityRate):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.NationalLevelGraph.SlidePositivityRateGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.NationalLevelGraph.SlidePositivityRateGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.NationalReport + " - " + DQAConstants.RDTPositivityRate):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.NationalLevelGraph.RDTPositivityRateGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.NationalLevelGraph.RDTPositivityRateGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.NationalReport + " - " + DQAConstants.ProportionOfSuspectsTested):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.NationalLevelGraph.ProportionOfSuspectsTestedGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.NationalLevelGraph.ProportionOfSuspectsTestedGraph.InitialReportColumn);


                            break;
                    }

                    break;

                //Province Level 
                case (DQAConstants.ProvinceReport + " - " + DQAConstants.ProportionOfMalariaOutpatients):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.ProvinceLevelGraph.ProportionOfMalariaOutpatientsGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.ProvinceLevelGraph.ProportionOfMalariaOutpatientsGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.ProvinceReport + " - " + DQAConstants.ProportionOfMalariaInpatients):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.ProvinceLevelGraph.ProportionOfMalariaInpatientsGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.ProvinceLevelGraph.ProportionOfMalariaInpatientsGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.ProvinceReport + " - " + DQAConstants.ProportionOfMalariaInpatientDeaths):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.ProvinceLevelGraph.ProportionOfMalariaInpatientDeathsGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.ProvinceLevelGraph.ProportionOfMalariaInpatientDeathsGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.ProvinceReport + " - " + DQAConstants.TestPositivityRate):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.ProvinceLevelGraph.TestPositivityRateGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.ProvinceLevelGraph.TestPositivityRateGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.ProvinceReport + " - " + DQAConstants.SlidePositivityRate):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.ProvinceLevelGraph.SlidePositivityRateGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.ProvinceLevelGraph.SlidePositivityRateGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.ProvinceReport + " - " + DQAConstants.RDTPositivityRate):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.ProvinceLevelGraph.RDTPositivityRateGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.ProvinceLevelGraph.RDTPositivityRateGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.ProvinceReport + " - " + DQAConstants.ProportionOfSuspectsTested):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.ProvinceLevelGraph.ProportionOfSuspectsTestedGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.ProvinceLevelGraph.ProportionOfSuspectsTestedGraph.InitialReportColumn);


                            break;
                    }

                    break;

                //District Level 
                case (DQAConstants.DistrictReport + " - " + DQAConstants.ProportionOfMalariaOutpatients):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.DistrictLevelGraph.ProportionOfMalariaOutpatientsGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.DistrictLevelGraph.ProportionOfMalariaOutpatientsGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.DistrictReport + " - " + DQAConstants.ProportionOfMalariaInpatients):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.DistrictLevelGraph.ProportionOfMalariaInpatientsGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.DistrictLevelGraph.ProportionOfMalariaInpatientsGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.DistrictReport + " - " + DQAConstants.ProportionOfMalariaInpatientDeaths):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.DistrictLevelGraph.ProportionOfMalariaInpatientDeathsGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.DistrictLevelGraph.ProportionOfMalariaInpatientDeathsGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.DistrictReport + " - " + DQAConstants.TestPositivityRate):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.DistrictLevelGraph.TestPositivityRateGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.DistrictLevelGraph.TestPositivityRateGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.DistrictReport + " - " + DQAConstants.SlidePositivityRate):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.DistrictLevelGraph.SlidePositivityRateGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.DistrictLevelGraph.SlidePositivityRateGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.DistrictReport + " - " + DQAConstants.RDTPositivityRate):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.DistrictLevelGraph.RDTPositivityRateGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.DistrictLevelGraph.RDTPositivityRateGraph.InitialReportColumn);


                            break;
                    }

                    break;

                case (DQAConstants.DistrictReport + " - " + DQAConstants.ProportionOfSuspectsTested):

                    switch (type)
                    {
                        case (int)DeskLevelGraphTypes.Type:

                            InsertTableInExcelSheet(graphSheet, graphReport, _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.DistrictLevelGraph.ProportionOfSuspectsTestedGraph.InitialReportRow,
                                                                     _dqaExcelSettings.DeskLevel.DeskLevelGraphsExcelSetting.ConsistencyOverTimeGraph.DistrictLevelGraph.ProportionOfSuspectsTestedGraph.InitialReportColumn);


                            break;
                    }

                    break;
            }
        }

        /// <summary>
        /// Get excel template response for elimination DQA
        /// </summary>
        /// <returns>Excel workbook response in byte array</returns>
        public byte[] GetEliminationTemplate()
        {
            using (MemoryStream memoryStream = new MemoryStream())
            {
                string templateFilePath = Path.Combine(Environment.CurrentDirectory, DQAConstants.DQATemplateFilePath, $"{_dqaExcelSettings.Elimination.TemplateFileName}.xlsx");

                // Check if template file exists
                if (!File.Exists(templateFilePath))
                {
                    throw new FileNotFoundException($"DQA Elimination template file not found at path: {templateFilePath}");
                }

                Console.WriteLine($"Processing DQA Elimination template from: {templateFilePath}");

                var workbook = new XLWorkbook(templateFilePath);
                workbook.SaveAs(memoryStream);
                return memoryStream.ToArray();
            }
        }
        #endregion

        #region Private Method

        /// <summary>
        /// Remove extra column from datatable which is not used in export report
        /// </summary>
        /// <param name="dataTable">Data Table</param>
        /// <returns>Data Table</returns>
        private DataTable RemoveExtraColumns(DataTable dataTable)
        {
            if (dataTable.Columns.Contains(ColumnName))
            {
                dataTable.Columns.Remove(dataTable.Columns[ColumnName]);
            }

            return dataTable;
        }

        /// <summary>
        /// Checks if service level summary sheet is valid
        /// </summary>
        /// <param name="summaryWorksheet">Summary work sheet which is to be checked</param>
        /// <param name="fileName">Name of the file which is uploaded</param>
        /// <returns>Return true if valid otherwise exception </returns>
        private bool IsServiceLevelSummarySheetValid(IXLWorksheet summaryWorksheet, string fileName)
        {
            //Reading excel summary sheet row from 10 to 21 for month concordance of core variable
            for (int rowCellIndex = 10; rowCellIndex <= 21; rowCellIndex++)
            {
                try
                {
                    object monthConcordanceVariable = summaryWorksheet.Cell($"C{rowCellIndex}").CachedValue;
                    if (monthConcordanceVariable is null)
                    {
                        throw new ExcelCellException(fileName, _dqaExcelSettings.ServiceLevel.SummaryWorksheet.SheetNumber, $"C{rowCellIndex}");
                    }
                }
                catch (Exception ex)
                {
                    throw new ExcelCellException(fileName, _dqaExcelSettings.ServiceLevel.SummaryWorksheet.SheetNumber, $"C{rowCellIndex}", ex);
                }
            }

            return true;
        }

        /// <summary>
        /// Modify hmis work sheet
        /// </summary>
        /// <param name="workbook">XLWorkbook</param>
        /// <param name="months">Months array</param>
        /// <param name="createTemplateInputModel">Create template input model</param>
        private void ModifyHMISWorkSheet(XLWorkbook workbook, string[] months, CreateTemplateInputModel createTemplateInputModel)
        {
            int firstCellRowIndex = 0;
            int lastCellRowIndex = 0;
            int firstCellCoumnIndex = 0;
            int lastCellColumnIndex = 0;

            HMISWorksheet hmisWorkSheetSetting = _dqaExcelSettings.ServiceLevel.HMISWorksheet;
            IXLWorksheet hmisDataWorksheet = workbook.Worksheet(hmisWorkSheetSetting.SheetNumber); //HMISData wokrSheet      

            hmisDataWorksheet.SetTabColor(XLColor.FromArgb(51, 153, 255)); //Light Blue
            IXLCells yearMonthRow = hmisDataWorksheet.Row(hmisWorkSheetSetting.YearMonthRowHeader.FirstCellRow).Cells(hmisWorkSheetSetting.YearMonthRowHeader.FirstCellColumn, hmisWorkSheetSetting.YearMonthRowHeader.LastCellColumn);
            hmisDataWorksheet.Protect();

            hmisDataWorksheet.Cell(hmisWorkSheetSetting.DateCell).SetValue<string>(createTemplateInputModel.StartDate.ToString(Domain.Constants.Constants.Common.ExcelDateFormat)).Style.Protection.SetHidden().Protection.SetLocked(true);

            int columnIndex = 0;
            int fromYearMonthColIndex = createTemplateInputModel.From.Month - 1;
            int toYearMonthColIndex = 0;
            // Grey out the column area that are not in the selected year month range and also give column header
            foreach (IXLCell cell in yearMonthRow)
            {
                string yearMonth = GetYearMonthName(fromYearMonthColIndex, toYearMonthColIndex, months, createTemplateInputModel.From, createTemplateInputModel.To);
                cell.DataType = XLDataType.Text;
                hmisDataWorksheet.Cell(cell.Address).SetValue<string>(Convert.ToString(yearMonth)).Style.Protection.SetHidden().Protection.SetLocked(true).Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                hmisDataWorksheet.Range(hmisWorkSheetSetting.ProtectedYearMonthRow.FirstCellRow, columnIndex + hmisWorkSheetSetting.ProtectedYearMonthRow.FirstCellColumn, hmisWorkSheetSetting.ProtectedYearMonthRow.LastCellRow, columnIndex + hmisWorkSheetSetting.ProtectedYearMonthRow.FirstCellColumn).Value = "";

                firstCellRowIndex = hmisWorkSheetSetting.YearMonthRowHeader.FirstCellRow;
                firstCellCoumnIndex = columnIndex + hmisWorkSheetSetting.YearMonthRowHeader.FirstCellColumn;
                lastCellRowIndex = hmisWorkSheetSetting.YearMonthRowHeader.LastCellRow;
                lastCellColumnIndex = columnIndex + hmisWorkSheetSetting.YearMonthRowHeader.FirstCellColumn;

                if (fromYearMonthColIndex >= createTemplateInputModel.From.Month && toYearMonthColIndex >= createTemplateInputModel.To.Month)
                {
                    hmisDataWorksheet.Range(firstCellRowIndex, firstCellCoumnIndex, lastCellRowIndex, lastCellColumnIndex).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                }
                else
                {
                    // If all core variable is selected then no need to lock this cell
                    if (createTemplateInputModel.VariableIds.Count() < 12)
                    {
                        RowColumnRange rowColumnRange = new RowColumnRange
                        {
                            FirstCellColumn = columnIndex + hmisWorkSheetSetting.CoreVariable.FirstCellColumn,
                            LastCellColumn = columnIndex + hmisWorkSheetSetting.CoreVariable.LastCellColumn,
                            FirstCellRow = hmisWorkSheetSetting.CoreVariable.FirstCellRow,
                            LastCellRow = hmisWorkSheetSetting.CoreVariable.LastCellRow
                        };

                        LockCoreVariableCell(hmisDataWorksheet, createTemplateInputModel.VariableIds, rowColumnRange);
                    }
                }

                if (fromYearMonthColIndex < 12)
                {
                    fromYearMonthColIndex++;

                    if (createTemplateInputModel.To.Month > createTemplateInputModel.From.Month && fromYearMonthColIndex >= createTemplateInputModel.From.Month)
                    {
                        toYearMonthColIndex = fromYearMonthColIndex;
                    }
                }
                else
                {
                    toYearMonthColIndex++;
                }

                columnIndex++;
            }

            IXLCells hmisMonthRowData = hmisDataWorksheet.Row(hmisWorkSheetSetting.HMISRowHeader.FirstCellRow).Cells(hmisWorkSheetSetting.HMISRowHeader.FirstCellColumn, hmisWorkSheetSetting.HMISRowHeader.LastCellColumn);
            int fromMonthColIndex = createTemplateInputModel.From.Month - 1;
            int toMonthColIndex = 0;
            // Giving a name to hmis column header that is selected from and to year month range
            foreach (IXLCell cell in hmisMonthRowData)
            {
                string hmisName = $"HMIS M{GetMonthIndex(fromMonthColIndex, toMonthColIndex)}";
                hmisDataWorksheet.Cell(cell.Address).SetValue<string>(Convert.ToString(hmisName)).Style.Protection.SetHidden().Protection.SetLocked(true).Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                if (fromMonthColIndex < 12)
                    fromMonthColIndex++;
                else
                    toMonthColIndex++;
            }

        }

        /// <summary>
        /// Modify Source Data  Work Sheet
        /// </summary>
        /// <param name="workbook">XLWorkbook</param>
        /// <param name="months">Months array</param>
        /// <param name="createTemplateInputModel">Create template input model</param>
        private void ModifySourceDataWorkSheet(XLWorkbook workbook, string[] months, CreateTemplateInputModel createTemplateInputModel)
        {
            int firstCellRowIndex = 0;
            int lastCellRowIndex = 0;
            int firstCellCoumnIndex = 0;
            int lastCellColumnIndex = 0;

            SourceWorksheet sourceWorkSheetSetting = _dqaExcelSettings.ServiceLevel.SourceWorksheet;
            IXLWorksheet sourceDataWorksheet = workbook.Worksheet(sourceWorkSheetSetting.SheetNumber); //Source Data workSheet      

            sourceDataWorksheet.SetTabColor(XLColor.FromArgb(51, 204, 51)); //LightGreen
            IXLCells yearMonthRow = sourceDataWorksheet.Row(sourceWorkSheetSetting.YearMonthRowHeader.FirstCellRow).Cells(sourceWorkSheetSetting.YearMonthRowHeader.FirstCellColumn, sourceWorkSheetSetting.YearMonthRowHeader.LastCellColumn);
            sourceDataWorksheet.Protect();

            sourceDataWorksheet.Cell(sourceWorkSheetSetting.DateCell).SetValue<string>(createTemplateInputModel.StartDate.ToString(Domain.Constants.Constants.Common.ExcelDateFormat)).Style.Protection.SetHidden().Protection.SetLocked(true);

            int columnIndex = 0;
            int fromYearMonthColIndex = createTemplateInputModel.From.Month - 1;
            int toYearMonthColIndex = 0;
            // Grey out the column area that are not in the selected year month range and also give column header
            foreach (IXLCell cell in yearMonthRow)
            {
                string yearMonth = GetYearMonthName(fromYearMonthColIndex, toYearMonthColIndex, months, createTemplateInputModel.From, createTemplateInputModel.To);
                cell.DataType = XLDataType.Text;
                sourceDataWorksheet.Cell(cell.Address).SetValue<string>(Convert.ToString(yearMonth)).Style.Protection.SetHidden().Protection.SetLocked(true).Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                firstCellRowIndex = sourceWorkSheetSetting.YearMonthRowHeader.FirstCellRow;
                firstCellCoumnIndex = columnIndex + sourceWorkSheetSetting.YearMonthRowHeader.FirstCellColumn;
                lastCellRowIndex = sourceWorkSheetSetting.YearMonthRowHeader.LastCellRow;
                lastCellColumnIndex = columnIndex + sourceWorkSheetSetting.YearMonthRowHeader.FirstCellColumn;

                if (fromYearMonthColIndex >= createTemplateInputModel.From.Month && toYearMonthColIndex >= createTemplateInputModel.To.Month)
                {
                    sourceDataWorksheet.Range(firstCellRowIndex, firstCellCoumnIndex, lastCellRowIndex, lastCellColumnIndex).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                }
                else
                {
                    // If all core variable is selected then no need to lock this cell
                    if (createTemplateInputModel.VariableIds.Count() < 12)
                    {
                        RowColumnRange rowColumnRange = new RowColumnRange
                        {
                            FirstCellColumn = columnIndex + sourceWorkSheetSetting.CoreVariable.FirstCellColumn,
                            LastCellColumn = columnIndex + sourceWorkSheetSetting.CoreVariable.LastCellColumn,
                            FirstCellRow = sourceWorkSheetSetting.CoreVariable.FirstCellRow,
                            LastCellRow = sourceWorkSheetSetting.CoreVariable.LastCellRow
                        };

                        LockCoreVariableCell(sourceDataWorksheet, createTemplateInputModel.VariableIds, rowColumnRange);
                    }
                }

                if (fromYearMonthColIndex < 12)
                {
                    fromYearMonthColIndex++;

                    if (createTemplateInputModel.To.Month > createTemplateInputModel.From.Month && fromYearMonthColIndex >= createTemplateInputModel.From.Month)
                    {
                        toYearMonthColIndex = fromYearMonthColIndex;
                    }
                }
                else
                {
                    toYearMonthColIndex++;
                }

                columnIndex++;
            }

            IXLCells sourceDataRow = sourceDataWorksheet.Row(sourceWorkSheetSetting.SourceRowHeader.FirstCellRow).Cells(sourceWorkSheetSetting.SourceRowHeader.FirstCellColumn, sourceWorkSheetSetting.SourceRowHeader.LastCellColumn);

            int fromMonthColIndex = createTemplateInputModel.From.Month - 1;
            int toMonthColIndex = 0;
            // Giving a name to hmis column header that is selected from and to year month range
            foreach (IXLCell cell in sourceDataRow)
            {
                string hmisName = $"Source M{GetMonthIndex(fromMonthColIndex, toMonthColIndex)}";
                sourceDataWorksheet.Cell(cell.Address).SetValue<string>(Convert.ToString(hmisName)).Style.Protection.SetHidden().Protection.SetLocked(true).Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                if (fromMonthColIndex < 12)
                    fromMonthColIndex++;
                else
                    toMonthColIndex++;
            }

        }

        /// <summary>
        /// Modify completeness core  work sheet
        /// </summary>
        /// <param name="workbook">XLWorkbook</param>    
        /// <param name="createTemplateInputModel">Create template input model</param>
        private void ModifyCompletenessCoreWorkSheet(XLWorkbook workbook, CreateTemplateInputModel createTemplateInputModel)
        {
            int firstCellRowIndex = 0;
            int lastCellRowIndex = 0;
            int firstCellCoumnIndex = 0;
            int lastCellColumnIndex = 0;

            CompletenessCoreWorksheet completenessCoreWorkSheetSetting = _dqaExcelSettings.ServiceLevel.CompletenessCoreWorksheet;
            IXLWorksheet completenessCoreResultWorksheet = workbook.Worksheet(completenessCoreWorkSheetSetting.SheetNumber);

            completenessCoreResultWorksheet.SetTabColor(XLColor.FromArgb(153, 0, 204)); //Indigo
            completenessCoreResultWorksheet.Protect();
            completenessCoreResultWorksheet.Cell(completenessCoreWorkSheetSetting.DateCell).SetValue<string>(createTemplateInputModel.StartDate.ToString(Domain.Constants.Constants.Common.ExcelDateFormat)).Style.Protection.SetHidden().Protection.SetLocked(true);

            completenessCoreResultWorksheet.Range(completenessCoreWorkSheetSetting.ProtectedTotalCompletenessRow.FirstCellRow, completenessCoreWorkSheetSetting.ProtectedTotalCompletenessRow.FirstCellColumn, completenessCoreWorkSheetSetting.ProtectedTotalCompletenessRow.LastCellRow, completenessCoreWorkSheetSetting.ProtectedTotalCompletenessRow.LastCellColumn).Style.Protection.SetLocked(true);

            IXLCells completeMonthRow = completenessCoreResultWorksheet.Row(completenessCoreWorkSheetSetting.CompletenessCoreRowHeader.FirstCellRow).Cells(completenessCoreWorkSheetSetting.CompletenessCoreRowHeader.FirstCellColumn, completenessCoreWorkSheetSetting.CompletenessCoreRowHeader.LastCellColumn);

            int cellIndex = 0;
            int fromYearMonthColIndex = createTemplateInputModel.From.Month - 1;
            int toYearMonthColIndex = 0;
            // Grey out the column area that are not in the selected year month range
            foreach (IXLCell cell in completeMonthRow)
            {
                string cellValue = Convert.ToString(cell.Value);
                if (!string.IsNullOrWhiteSpace(cellValue))
                {
                    if (fromYearMonthColIndex >= createTemplateInputModel.From.Month && toYearMonthColIndex >= createTemplateInputModel.To.Month)
                    {
                        firstCellRowIndex = completenessCoreWorkSheetSetting.ProtectedCompletenessCoreRow.FirstCellRow;
                        firstCellCoumnIndex = cellIndex + completenessCoreWorkSheetSetting.ProtectedCompletenessCoreRow.FirstCellColumn;
                        lastCellRowIndex = completenessCoreWorkSheetSetting.ProtectedCompletenessCoreRow.LastCellRow;
                        lastCellColumnIndex = cellIndex + completenessCoreWorkSheetSetting.ProtectedCompletenessCoreRow.LastCellColumn;
                        completenessCoreResultWorksheet.Range(firstCellRowIndex, firstCellCoumnIndex, lastCellRowIndex, lastCellColumnIndex).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                    }
                    else
                    {
                        // Protected outpatient register cell if not selected 
                        if (!createTemplateInputModel.RegisterTypes.Any(id => id == (int)DQASLRegisterType.OutpatientRegister))
                        {
                            firstCellRowIndex = completenessCoreWorkSheetSetting.ProtectedOutPatientRegisterRow.FirstCellRow;
                            firstCellCoumnIndex = cellIndex + completenessCoreWorkSheetSetting.ProtectedOutPatientRegisterRow.FirstCellColumn;
                            lastCellRowIndex = completenessCoreWorkSheetSetting.ProtectedOutPatientRegisterRow.LastCellRow;
                            lastCellColumnIndex = cellIndex + completenessCoreWorkSheetSetting.ProtectedOutPatientRegisterRow.LastCellColumn;
                            completenessCoreResultWorksheet.Range(firstCellRowIndex, firstCellCoumnIndex, lastCellRowIndex, lastCellColumnIndex).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                        }
                        // Protected inpatient register cell if not selected 
                        if (!createTemplateInputModel.RegisterTypes.Any(id => id == (int)DQASLRegisterType.InpatientRegister))
                        {
                            firstCellRowIndex = completenessCoreWorkSheetSetting.ProtectedInPatientRegisterRow.FirstCellRow;
                            firstCellCoumnIndex = cellIndex + completenessCoreWorkSheetSetting.ProtectedInPatientRegisterRow.FirstCellColumn;
                            lastCellRowIndex = completenessCoreWorkSheetSetting.ProtectedInPatientRegisterRow.LastCellRow;
                            lastCellColumnIndex = cellIndex + completenessCoreWorkSheetSetting.ProtectedInPatientRegisterRow.LastCellColumn;
                            completenessCoreResultWorksheet.Range(firstCellRowIndex, firstCellCoumnIndex, lastCellRowIndex, lastCellColumnIndex).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                        }
                        // Protected lab register cell if not selected
                        if (!createTemplateInputModel.RegisterTypes.Any(id => id == (int)DQASLRegisterType.LabRegister))
                        {
                            firstCellRowIndex = completenessCoreWorkSheetSetting.ProtectedLabRegisterRow.FirstCellRow;
                            firstCellCoumnIndex = cellIndex + completenessCoreWorkSheetSetting.ProtectedLabRegisterRow.FirstCellColumn;
                            lastCellRowIndex = completenessCoreWorkSheetSetting.ProtectedLabRegisterRow.LastCellRow;
                            lastCellColumnIndex = cellIndex + completenessCoreWorkSheetSetting.ProtectedLabRegisterRow.LastCellColumn;
                            completenessCoreResultWorksheet.Range(firstCellRowIndex, firstCellCoumnIndex, lastCellRowIndex, lastCellColumnIndex).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                        }

                        //Formula Row Protected
                        firstCellRowIndex = completenessCoreWorkSheetSetting.ProtectedFormulaRow.FirstCellRow;
                        firstCellCoumnIndex = cellIndex + completenessCoreWorkSheetSetting.ProtectedFormulaRow.FirstCellColumn;
                        lastCellRowIndex = completenessCoreWorkSheetSetting.ProtectedFormulaRow.LastCellRow;
                        lastCellColumnIndex = cellIndex + completenessCoreWorkSheetSetting.ProtectedFormulaRow.LastCellColumn;
                        completenessCoreResultWorksheet.Range(firstCellRowIndex, firstCellCoumnIndex, lastCellRowIndex, lastCellColumnIndex).Style.Protection.SetLocked(true);
                    }

                    if (fromYearMonthColIndex < 12)
                    {
                        fromYearMonthColIndex++;
                        if (createTemplateInputModel.To.Month > createTemplateInputModel.From.Month && fromYearMonthColIndex >= createTemplateInputModel.From.Month)
                        {
                            toYearMonthColIndex = fromYearMonthColIndex;
                        }
                    }
                    else
                    {
                        toYearMonthColIndex++;
                    }
                }
                cellIndex++;
            }

        }

        /// <summary>
        /// Modify concordance work sheet
        /// </summary>
        /// <param name="workbook">XLWorkbook</param>    
        /// <param name="createTemplateInputModel">Create template input model</param>
        private void ModifyConcordanceWorkSheet(XLWorkbook workbook, CreateTemplateInputModel createTemplateInputModel)
        {
            int firstCellRowIndex = 0;
            int lastCellRowIndex = 0;
            int firstCellCoumnIndex = 0;
            int lastCellColumnIndex = 0;

            ConcordanceWorksheet concordanceWorkSheetSetting = _dqaExcelSettings.ServiceLevel.ConcordanceWorksheet;
            IXLWorksheet concordanceWorksheet = workbook.Worksheet(concordanceWorkSheetSetting.SheetNumber); //1.2.12 Concordance Worksheet

            concordanceWorksheet.SetTabColor(XLColor.FromArgb(153, 0, 204)); //Indigo
            concordanceWorksheet.Protect();
            concordanceWorksheet.Cell(concordanceWorkSheetSetting.DateCell).SetValue<string>(createTemplateInputModel.StartDate.ToString(Domain.Constants.Constants.Common.ExcelDateFormat)).Style.Protection.SetHidden().Protection.SetLocked(true);

            IXLCells concordanceMonthRow = concordanceWorksheet.Row(concordanceWorkSheetSetting.ConcordanceRowHeader.FirstCellRow).Cells(concordanceWorkSheetSetting.ConcordanceRowHeader.FirstCellColumn, concordanceWorkSheetSetting.ConcordanceRowHeader.LastCellRow);

            int concordanceCellIndex = 0;
            int fromYearMonthColIndex = createTemplateInputModel.From.Month - 1;
            int toYearMonthColIndex = 0;

            // Grey out the column area that are not in the selected year month range
            foreach (IXLCell cell in concordanceMonthRow)
            {
                string cellValue = Convert.ToString(cell.Value);
                if (!string.IsNullOrWhiteSpace(cellValue))
                {
                    if (fromYearMonthColIndex >= createTemplateInputModel.From.Month && toYearMonthColIndex >= createTemplateInputModel.To.Month)
                    {
                        firstCellRowIndex = concordanceWorkSheetSetting.ProtectedConcordanceRow.FirstCellRow;
                        firstCellCoumnIndex = concordanceCellIndex + concordanceWorkSheetSetting.ProtectedConcordanceRow.FirstCellColumn;
                        lastCellRowIndex = concordanceWorkSheetSetting.ProtectedConcordanceRow.LastCellRow;
                        lastCellColumnIndex = concordanceCellIndex + concordanceWorkSheetSetting.ProtectedConcordanceRow.LastCellColumn;
                        concordanceWorksheet.Range(firstCellRowIndex, firstCellCoumnIndex, lastCellRowIndex, lastCellColumnIndex).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                    }
                    else
                    {
                        // If all core variable is selected then no need to lock this cell
                        if (createTemplateInputModel.VariableIds.Count() < 12)
                        {
                            RowColumnRange rowColumnRange = new RowColumnRange
                            {
                                FirstCellColumn = concordanceCellIndex + concordanceWorkSheetSetting.CoreVariable.FirstCellColumn,
                                LastCellColumn = concordanceCellIndex + concordanceWorkSheetSetting.CoreVariable.LastCellColumn,
                                FirstCellRow = concordanceWorkSheetSetting.CoreVariable.FirstCellRow,
                                LastCellRow = concordanceWorkSheetSetting.CoreVariable.LastCellRow
                            };

                            LockCoreVariableCell(concordanceWorksheet, createTemplateInputModel.VariableIds, rowColumnRange);
                        }
                    }

                    if (fromYearMonthColIndex < 12)
                    {
                        fromYearMonthColIndex++;
                        if (createTemplateInputModel.To.Month > createTemplateInputModel.From.Month && fromYearMonthColIndex >= createTemplateInputModel.From.Month)
                        {
                            toYearMonthColIndex = fromYearMonthColIndex;
                        }
                    }
                    else
                    {
                        toYearMonthColIndex++;
                    }
                }

                concordanceCellIndex++;
            }

            IXLCells concoradanceErrorRow = concordanceWorksheet.Row(concordanceWorkSheetSetting.ConcordanceRowErrorHeader.FirstCellRow).Cells(concordanceWorkSheetSetting.ConcordanceRowErrorHeader.FirstCellColumn, concordanceWorkSheetSetting.ConcordanceRowErrorHeader.LastCellRow);

            int concordanceErrorColumnIndex = 0;
            int fromMonthColIndex = createTemplateInputModel.From.Month - 1;
            int toMonthColIndex = 0;
            // Giving a name to concordance error column header that is selected from and to year month range
            foreach (IXLCell cell in concoradanceErrorRow)
            {
                if (concordanceErrorColumnIndex % 2 == 0)
                {
                    string concoreName = $"Error M{GetMonthIndex(fromMonthColIndex, toMonthColIndex)}";
                    concordanceWorksheet.Cell(cell.Address).SetValue<string>(Convert.ToString(concoreName)).Style.Protection.SetHidden().Protection.SetLocked(true).Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                    if (fromMonthColIndex < 12)
                        fromMonthColIndex++;
                    else
                        toMonthColIndex++;
                }

                concordanceErrorColumnIndex++;
            }
        }

        /// <summary>
        /// Modify observed result work sheet
        /// </summary>
        /// <param name="workbook">XLWorkbook</param>    
        /// <param name="createTemplateInputModel">Create template input model</param>
        private void ModifyObservedResultWorkSheet(XLWorkbook workbook, CreateTemplateInputModel createTemplateInputModel)
        {
            ObservedWorksheet observedWorksheetSetting = _dqaExcelSettings.ServiceLevel.ObservedWorksheet;
            IXLWorksheet observedResultWorksheet = workbook.Worksheet(observedWorksheetSetting.SheetNumber);
            observedResultWorksheet.SetTabColor(XLColor.FromArgb(153, 0, 204)); //Indigo
            observedResultWorksheet.Protect();
            observedResultWorksheet.Cell(observedWorksheetSetting.DateCell).SetValue<string>(createTemplateInputModel.StartDate.ToString(Domain.Constants.Constants.Common.ExcelDateFormat)).Style.Protection.SetHidden().Protection.SetLocked(true);
            observedResultWorksheet.Range(observedWorksheetSetting.ProtectedObservedRow.FirstCellRow, observedWorksheetSetting.ProtectedObservedRow.FirstCellColumn, observedWorksheetSetting.ProtectedObservedRow.LastCellRow, observedWorksheetSetting.ProtectedObservedRow.LastCellColumn).Style.Protection.SetLocked(false);
        }

        /// <summary>
        /// Modify correction work sheet
        /// </summary>
        /// <param name="workbook">workbook</param>    
        /// <param name="createTemplateInputModel">Create Template Input Model</param>
        private void ModifyCorrectionWorkSheet(XLWorkbook workbook, CreateTemplateInputModel createTemplateInputModel)
        {
            int firstCellRowIndex = 0;
            int lastCellRowIndex = 0;
            int firstCellCoumnIndex = 0;
            int lastCellColumnIndex = 0;

            int totalSheets = workbook.Worksheets.Count;
            int sheetNumber = _dqaExcelSettings.ServiceLevel.CorrectionWorksheet.SheetNumber;
            if (sheetNumber <= totalSheets)
            {
                IXLWorksheet correctionResultWorksheet = workbook.Worksheet(_dqaExcelSettings.ServiceLevel.CorrectionWorksheet.SheetNumber);

                correctionResultWorksheet.SetTabColor(XLColor.FromArgb(194, 194, 163)); //Grey
                correctionResultWorksheet.Protect();
                correctionResultWorksheet.Cell(_dqaExcelSettings.ServiceLevel.CorrectionWorksheet.DateCell).SetValue<string>(createTemplateInputModel.StartDate.ToString(Domain.Constants.Constants.Common.ExcelDateFormat)).Style.Protection.SetHidden().Protection.SetLocked(true);

                IXLCells correctionResultRow = correctionResultWorksheet.Row(_dqaExcelSettings.ServiceLevel.CorrectionWorksheet.CorrectionRowHeader.FirstCellRow).Cells(_dqaExcelSettings.ServiceLevel.CorrectionWorksheet.CorrectionRowHeader.FirstCellColumn, _dqaExcelSettings.ServiceLevel.CorrectionWorksheet.CorrectionRowHeader.LastCellColumn);

                int columnIndex = 0;
                int fromYearMonthColIndex = createTemplateInputModel.From.Month - 1;
                int toYearMonthColIndex = 0;
                int toMonthColIndex = 0;
                // Grey out the column area that are not in the selected year month range and also give column header
                foreach (IXLCell cell in correctionResultRow)
                {
                    string correctionName = $"Correct M{GetMonthIndex(fromYearMonthColIndex, toMonthColIndex)}";
                    correctionResultWorksheet.Cell(cell.Address).SetValue<string>(Convert.ToString(correctionName))
                    .Style
                   .Protection.SetHidden()
                   .Protection.SetLocked(true)
                   .Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                    if (fromYearMonthColIndex >= createTemplateInputModel.From.Month && toYearMonthColIndex >= createTemplateInputModel.To.Month)
                    {
                        firstCellRowIndex = _dqaExcelSettings.ServiceLevel.CorrectionWorksheet.ProtectedCorrectionRow.FirstCellRow;
                        firstCellCoumnIndex = columnIndex + _dqaExcelSettings.ServiceLevel.CorrectionWorksheet.ProtectedCorrectionRow.FirstCellColumn;
                        lastCellRowIndex = _dqaExcelSettings.ServiceLevel.CorrectionWorksheet.ProtectedCorrectionRow.LastCellRow;
                        lastCellColumnIndex = columnIndex + _dqaExcelSettings.ServiceLevel.CorrectionWorksheet.ProtectedCorrectionRow.LastCellColumn;

                        correctionResultWorksheet.Range(firstCellRowIndex, firstCellCoumnIndex, lastCellRowIndex, lastCellColumnIndex).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                    }

                    if (fromYearMonthColIndex < 12)
                    {
                        fromYearMonthColIndex++;
                        if (createTemplateInputModel.To.Month > createTemplateInputModel.From.Month && fromYearMonthColIndex >= createTemplateInputModel.From.Month)
                        {
                            toYearMonthColIndex = fromYearMonthColIndex;
                        }
                    }
                    else
                    {
                        toYearMonthColIndex++;
                        toMonthColIndex++;
                    }

                    columnIndex++;
                }
            }
        }

        /// <summary>
        /// Lock core variable cells
        /// </summary>
        /// <param name="worksheet">Cells to be locked from this worksheet</param>
        /// <param name="variableIds">Selected core variable ids of service level template</param>
        /// <param name="rowColumnRange">Range of row and column</param>
        private void LockCoreVariableCell(IXLWorksheet worksheet, IEnumerable<Guid> variableIds, RowColumnRange rowColumnRange)
        {

            if (!variableIds.Any(id => id == DQAVariableSeedingMetadata.TotalMalariaCases_ID))
            {
                worksheet.Range(rowColumnRange.FirstCellRow, rowColumnRange.FirstCellColumn, rowColumnRange.LastCellRow, rowColumnRange.LastCellColumn).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
            }

            if (!variableIds.Any(id => id == DQAVariableSeedingMetadata.ConfirmedMalariaCases_ID))
            {
                worksheet.Range(rowColumnRange.FirstCellRow + 1, rowColumnRange.FirstCellColumn, rowColumnRange.LastCellRow + 1, rowColumnRange.LastCellColumn).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
            }

            if (!variableIds.Any(id => id == DQAVariableSeedingMetadata.MicroscopyTested_ID))
            {
                worksheet.Range(rowColumnRange.FirstCellRow + 2, rowColumnRange.FirstCellColumn, rowColumnRange.LastCellRow + 2, rowColumnRange.LastCellColumn).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
            }

            if (!variableIds.Any(id => id == DQAVariableSeedingMetadata.RDTTested_ID))
            {
                worksheet.Range(rowColumnRange.FirstCellRow + 3, rowColumnRange.FirstCellColumn, rowColumnRange.LastCellRow + 3, rowColumnRange.LastCellColumn).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
            }

            if (!variableIds.Any(id => id == DQAVariableSeedingMetadata.MicroscopyPositive_ID))
            {
                worksheet.Range(rowColumnRange.FirstCellRow + 4, rowColumnRange.FirstCellColumn, rowColumnRange.LastCellRow + 4, rowColumnRange.LastCellColumn).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
            }

            if (!variableIds.Any(id => id == DQAVariableSeedingMetadata.RDTPositive_ID))
            {
                worksheet.Range(rowColumnRange.FirstCellRow + 5, rowColumnRange.FirstCellColumn, rowColumnRange.LastCellRow + 5, rowColumnRange.LastCellColumn).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
            }

            if (!variableIds.Any(id => id == DQAVariableSeedingMetadata.AllCauseOutpatient_ID))
            {
                worksheet.Range(rowColumnRange.FirstCellRow + 6, rowColumnRange.FirstCellColumn, rowColumnRange.LastCellRow + 6, rowColumnRange.LastCellColumn).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
            }

            if (!variableIds.Any(id => id == DQAVariableSeedingMetadata.AllCauseInpatient_ID))
            {
                worksheet.Range(rowColumnRange.FirstCellRow + 7, rowColumnRange.FirstCellColumn, rowColumnRange.LastCellRow + 7, rowColumnRange.LastCellColumn).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
            }

            if (!variableIds.Any(id => id == DQAVariableSeedingMetadata.AllCauseDeath_ID))
            {
                worksheet.Range(rowColumnRange.FirstCellRow + 8, rowColumnRange.FirstCellColumn, rowColumnRange.LastCellRow + 8, rowColumnRange.LastCellColumn).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
            }

            if (!variableIds.Any(id => id == DQAVariableSeedingMetadata.MalariaInpatient_ID))
            {
                worksheet.Range(rowColumnRange.FirstCellRow + 9, rowColumnRange.FirstCellColumn, rowColumnRange.LastCellRow + 9, rowColumnRange.LastCellColumn).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
            }

            if (!variableIds.Any(id => id == DQAVariableSeedingMetadata.MalariaInpatientDeath_ID))
            {
                worksheet.Range(rowColumnRange.FirstCellRow + 10, rowColumnRange.FirstCellColumn, rowColumnRange.LastCellRow + 10, rowColumnRange.LastCellColumn).Style.Protection.SetLocked(true).Fill.SetBackgroundColor(XLColor.Gray).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
            }
        }

        /// <summary>
        /// Get year and month name combination for template year month header row
        /// </summary>
        /// <param name="fromMonthIndex">Index of from month</param>
        /// <param name="toMonthIndex">Index of to month</param>
        /// <param name="months">Array of months</param>
        /// <param name="from">Datetime of from period</param>
        /// <param name="to">Datetime of to period</param>
        /// <returns> combination of year month name</returns>
        private string GetYearMonthName(int fromMonthIndex, int toMonthIndex, string[] months, DateTime from, DateTime to)
        {
            string yearMonth = "";
            string monthName = "";
            if (fromMonthIndex < 12)
            {
                monthName = months[fromMonthIndex];
                yearMonth = $"{from.Year} {monthName}";
            }
            else
            {
                if (toMonthIndex == fromMonthIndex && from.Year == to.Year)
                {
                    monthName = months[toMonthIndex - fromMonthIndex];
                    yearMonth = $"{to.Year + 1} {monthName}";
                }
                else if (toMonthIndex < 12 && (from.Year == to.Year && to.Month < 12))
                {
                    monthName = months[toMonthIndex];
                    yearMonth = $"{to.Year} {monthName}";
                }
                else if (toMonthIndex < 12)
                {
                    monthName = months[toMonthIndex];
                    yearMonth = $"{to.Year} {monthName}";
                }
                else
                {
                    monthName = months[toMonthIndex - months.Length];
                    yearMonth = $"{to.Year + 1} {monthName}";
                }
            }
            return yearMonth;
        }

        /// <summary>
        /// Get month index for template month header row
        /// </summary>
        /// <param name="fromMonthIndex">Index of from month</param>
        /// <param name="toMonthIndex">Index of to month</param>
        /// <returns>month index</returns>
        private int GetMonthIndex(int fromMonthIndex, int toMonthIndex)
        {
            int monthIndex = 0;
            if (fromMonthIndex < 12)
            {
                monthIndex = fromMonthIndex + 1;
            }
            else
            {
                monthIndex = toMonthIndex + 1;
            }
            return monthIndex;
        }

        /// <summary>
        /// Method to convert List into Datatable
        /// </summary>
        /// <param name="items">List of strings</param>
        /// <returns>Instance of Datatable</returns>
        private DataTable ToDataTable(List<string> items)
        {
            DataTable dataTable = new DataTable();
            //Get all the properties by using reflection   
            foreach (string item in items)
            {
                //Setting column names as Property names  
                dataTable.Columns.Add(item, typeof(String));
            }

            return dataTable;
        }

        /// <summary>
        /// Method return the numbers and color code 
        /// </summary>
        /// <param name="dataTable">Summary table</param>
        /// <param name="language">languge code</param>
        /// <returns>List<(string, XLColor)></returns>
        private List<(string, XLColor)> GetColorsRule(DataTable dataTable, string language)
        {
            List<(string, XLColor)> tupleList = new List<(string, XLColor)>();

            for (int i = 0; i < 5; i++)
            {
                string columnName = _translationService.GetTranslation("Assessment.DataCollection.DataQualityAssessment.NationalLevelResults");

                int value = Convert.ToInt32(dataTable.Rows[i][columnName]);

                if (value > 95 && value <= 100)
                {
                    tupleList.Add((value.ToString(), XLColor.FromArgb(198, 239, 206)));
                }
                else if (value <= 95 && value >= 80)
                {
                    tupleList.Add((value.ToString(), XLColor.FromArgb(255, 235, 156)));
                }
                else if ((value < 80 && value >= 0) || value > 100)
                {
                    tupleList.Add((value.ToString(), XLColor.FromArgb(255, 199, 206)));
                }
                else
                {
                    tupleList.Add((value.ToString(), XLColor.FromArgb(255, 199, 206)));
                }
            }
            return tupleList;
        }


        /// <summary>
        /// Insert table in excel sheet one by one cell with table header and rows
        /// </summary>
        /// <param name="graphSheet">graph Sheet</param>
        /// <param name="dataTable">Graph source table</param>
        /// <param name="initialRowNumber"> row number from table start </param>
        /// <param name="initialColumnNumber"> column number from table start</param>
        private void InsertTableInExcelSheet(IXLWorksheet graphSheet, DataTable dataTable, int initialRowNumber, int initialColumnNumber)
        {

            //insert header cells value 
            int i = 0;
            int yearColumnsCountInDataTable = 0;
            int lastYearInDatatable = 0;
            int lastColumnNum = 0;

            foreach (DataColumn dataColumn in dataTable.Columns)
            {
                int columnName;
                int.TryParse(dataColumn.ColumnName, out columnName);

                IXLCell headerCell = graphSheet.Cell(initialRowNumber, initialColumnNumber + i);

                ApplyStyleToExcelCell(headerCell, true);

                // check for number and string value
                if (columnName > 0)
                {
                    //number(int) value
                    headerCell.SetValue<int>(columnName);
                    lastYearInDatatable = columnName;
                    yearColumnsCountInDataTable++;
                    lastYearInDatatable = columnName;
                    lastColumnNum = initialRowNumber + i;
                }
                else
                {
                    // string Value
                    headerCell.SetValue<string>(dataColumn.ColumnName);
                }

                i++;
            }

            // for matching x-axis with extacts years  we have to add extra years
            if (yearColumnsCountInDataTable < DQAConstants.MinmunNumberOfYearsToShowInGraphXAxis)
            {
                int remainingYearCount = DQAConstants.MinmunNumberOfYearsToShowInGraphXAxis - yearColumnsCountInDataTable;
                for (int j = 1; j <= remainingYearCount; j++)
                {
                    IXLCell extraheaderCell = graphSheet.Cell(initialRowNumber, lastColumnNum + j);
                    ApplyStyleToExcelCell(extraheaderCell, true, true);
                    //int value
                    extraheaderCell.SetValue<int>(lastYearInDatatable + j);
                }
            }

            // Insert datatable rows data in excel one by one cell 
            int newRowNum = initialRowNumber + 1;
            // iterate through each rows
            foreach (DataRow dataRow in dataTable.Rows)
            {
                i = 0;

                // iterate through each column in current row
                foreach (DataColumn dataColumn in dataTable.Columns)
                {
                    int cellValue;

                    string value = dataRow[dataColumn.ColumnName].ToString();

                    int.TryParse(value, out cellValue);

                    IXLCell rowCell = graphSheet.Cell(newRowNum, initialColumnNumber + i);

                    ApplyStyleToExcelCell(rowCell, false);

                    if (cellValue > 0)
                    {
                        //(number value) int value
                        rowCell.SetValue<int>(cellValue);
                    }
                    else
                    {
                        // string Value
                        rowCell.SetValue<string>(value);
                    }
                    i++;
                }
                newRowNum++;
            }
        }

        /// <summary>
        /// Method to apply style to excel cell wheather it is header cell,extra header cell or row cell in table
        /// </summary>
        /// <param name="xlCell">excel cell object</param>
        /// <param name="isHeader"> boolen value </param>
        /// <param name="hasExtraHeader">boolen value</param>
        /// <returns>excel cell object</returns>
        private IXLCell ApplyStyleToExcelCell(IXLCell xlCell, bool isHeader = false, bool hasExtraHeader = false)
        {
            XLColor whiteColor = XLColor.White;
            XLColor grayColor = XLColor.BlueGray;
            XLBorderStyleValues xLBorderStyleValues = XLBorderStyleValues.Thin;


            if (isHeader)
            {
                xlCell.Style.Font.SetFontColor(whiteColor);
                xlCell.Style.Font.SetFontSize(10);
                xlCell.Style.Font.SetBold(true);

                if (!hasExtraHeader)
                {
                    xlCell.Style.Fill.SetBackgroundColor(grayColor);
                    xlCell.Style.Border.SetBottomBorder(xLBorderStyleValues);
                    xlCell.Style.Border.SetLeftBorder(xLBorderStyleValues);
                    xlCell.Style.Border.SetRightBorder(xLBorderStyleValues);
                    xlCell.Style.Border.SetTopBorder(xLBorderStyleValues);

                    xlCell.Style.Border.SetBottomBorderColor(grayColor);
                    xlCell.Style.Border.SetLeftBorderColor(grayColor);
                    xlCell.Style.Border.SetRightBorderColor(grayColor);
                    xlCell.Style.Border.SetTopBorderColor(grayColor);
                }
            }
            else
            {
                xlCell.Style.Border.SetBottomBorder(xLBorderStyleValues);
                xlCell.Style.Border.SetLeftBorder(xLBorderStyleValues);
                xlCell.Style.Border.SetRightBorder(xLBorderStyleValues);
                xlCell.Style.Border.SetTopBorder(xLBorderStyleValues);

                xlCell.Style.Border.SetBottomBorderColor(grayColor);
                xlCell.Style.Border.SetLeftBorderColor(grayColor);
                xlCell.Style.Border.SetRightBorderColor(grayColor);
                xlCell.Style.Border.SetTopBorderColor(grayColor);
            }

            return xlCell;
        }
        #endregion
    }
}
